<script setup lang="ts">
import ClassicEditor from "@/components/editors/Vditor.vue";
import {
  inject,
  reactive,
  type Ref,
  ref,
  watch,
  onMounted,
  onUnmounted,
  nextTick,
} from "vue";
import { ElMessage, type FormInstance, type FormRules } from "element-plus";
import {
  getTextSection,
  type params4submitTextSection,
  submitTextSection,
} from "@/apis/path/createProject";
import { emitter } from "@/utils/emitter";
import { Event } from "@/types/event";
import {
  convertImgTagLongUrls,
  convertLanguageMathToScript,
  convertMathFormulas,
  revertMathScriptsToMd,
} from "@/utils/latexUtils";

let editorRef = ref();
// 本组件进行前后端通信
const emits = defineEmits(["save:chapter"]);
const props = defineProps({
  prjId: Number,
  chapter: Object,
  showTitle: {
    type: Boolean,
    default: false,
  },
});

const ruleFormRef = ref<FormInstance>();
const ruleForm = reactive({
  sectionId: -1,
  sectionTitle: "",
  sectionNum: 0,
  prText: "",
});
// 自定义校验函数：检查编辑器实际内容
const validateEditorContent = (_rule: any, _value: any, callback: any) => {
  // 获取编辑器实际内容
  const editorContent = editorRef.value?.getData() || "";
  const trimmedContent = editorContent.trim();

  if (!trimmedContent) {
    callback(new Error("请输入内容"));
  } else {
    callback();
  }
};

const rules = reactive<FormRules<typeof ruleForm>>({
  sectionTitle: [{ required: true, message: "请输入标题", trigger: "blur" }],
  prText: [{ validator: validateEditorContent, trigger: "blur" }],
});

// sectionTitle 双模式编辑器相关变量
const sectionTitleEditMode = ref(false);
const sectionTitleInputRef = ref();

// const initData = () => {
//   getTextSection(ruleForm.sectionId).then((res) => {
//     if (res.success) {
//       const textSection = res.data.wordsContent.projectSections[0];
//       ruleForm.sectionTitle = revertMathScriptsToMd(textSection.sectionTitle);
//       ruleForm.prText = textSection.prText;

//       // 初始化 sectionTitle 编辑器状态
//       if (ruleForm.sectionTitle && ruleForm.sectionTitle.trim()) {
//         sectionTitleEditMode.value = false; // 有内容时默认显示预览模式
//       } else {
//         sectionTitleEditMode.value = true; // 无内容时默认显示编辑模式
//       }
//     } else {
//       ElMessage.error(res.message);
//     }
//   });
// };

// sectionTitle 编辑器相关方法
const handleSectionTitleBlur = () => {
  if (ruleForm.sectionTitle.trim()) {
    sectionTitleEditMode.value = false;
  }
};

const handleSectionTitlePreviewClick = () => {
  sectionTitleEditMode.value = true;
  nextTick(() => {
    if (sectionTitleInputRef.value) {
      sectionTitleInputRef.value.focus();
    }
  });
};

const handleSectionTitleFocus = () => {
  sectionTitleEditMode.value = true;
};

// 提交
const saveSubmitChapter = (): Promise<Boolean> => {
  return new Promise((resolve, reject) => {
    // 构造参数
    const param: params4submitTextSection = {
      projectId: props.prjId || 0,
      projectSections: [
        {
          sectionId: ruleForm.sectionId,
          sectionNum: ruleForm.sectionNum,
          sectionTitle: ruleForm.sectionTitle,
          // prText: convertLongUrlMarkdownImages(editorRef.value.getData()),
          prText: convertLanguageMathToScript(
            convertImgTagLongUrls(editorRef.value.getHtml())
          ),
          sectionContent: [],
          // htmlContent: markdownToHtml(
          //   (convertLongUrlMarkdownImages(ruleForm.prText))
          // ),
          // htmlContent: convertLanguageMathToScript(
          //   convertImgTagLongUrls(editorRef.value.getHtml())
          // ),
        },
      ],
    };

    // 调用 submitTextSection 并处理结果
    submitTextSection(param)
      .then((res) => {
        if (res.success) {
          ElMessage.success("保存成功");
          resolve(true);
        } else {
          ElMessage.error(res.message);
          resolve(false);
        }
      })
      .catch((error) => {
        reject(error);
      });
  });
};

// 提交 or 草稿
const handleSaveChapter = (submit?: boolean) => {
  return new Promise<Boolean>((resolve, reject) => {
    if (!ruleFormRef.value) {
      reject(new Error("ruleFormRef is not defined"));
      return;
    }
    if (submit) {
      console.log("submit", submit);
      ruleFormRef.value.validate((valid) => {
        if (valid) {
          saveSubmitChapter()
            .then(() => {
              resolve(true);
            })
            .catch((error) => {
              reject(error);
            });
        }
      });
    } else {
      saveSubmitChapter()
        .then(() => {
          resolve(true);
        })
        .catch((error) => {
          reject(error);
        });
    }
  });
};

const handleSave = (param: { status: number; id: number }) => {
  console.log("param", param, ruleForm.sectionId);
  if (param.id == ruleForm.sectionId) {
    handleSaveChapter(true).then(() => {
      emits("save:chapter", param);
    });
  }
};

watch(
  () => props,
  async (newValue) => {
    Object.assign(ruleForm, newValue.chapter);

    // 初始化 sectionTitle 编辑器状态
    if (ruleForm.sectionTitle && ruleForm.sectionTitle.trim()) {
      ruleForm.sectionTitle = revertMathScriptsToMd(ruleForm.sectionTitle);
      sectionTitleEditMode.value = false; // 有内容时默认显示预览模式
    } else {
      sectionTitleEditMode.value = true; // 无内容时默认显示编辑模式
    }

    // 等待下一个 tick，确保子组件已经挂载
    await nextTick();
    if (ruleForm.prText && editorRef.value.html2MdWhenReady) {
      // 使用新的异步转换方法
      ruleForm.prText = await editorRef.value.html2MdWhenReady(ruleForm.prText);
    }

    // 检查 sectionId 是否为 -1，如果是，则不执行 initData
  },
  { deep: true, immediate: true }
);
onMounted(() => {
  emitter.on(Event.SAVE_CHAPTER, handleSave);
});
onUnmounted(() => {
  emitter.off(Event.SAVE_CHAPTER, handleSave);
});
</script>
<template>
  <div class="text-content-wrapper">
    <el-form
      ref="ruleFormRef"
      :model="ruleForm"
      :rules="rules"
      style="width: 100%"
    >
      <el-form-item
        prop="sectionTitle"
        style="width: 100%"
        v-if="props.showTitle"
      >
        <!-- 编辑模式：显示输入框 -->
        <el-input
          v-if="sectionTitleEditMode"
          ref="sectionTitleInputRef"
          v-model="ruleForm.sectionTitle"
          placeholder="请输入标题，支持数学公式"
          @blur="handleSectionTitleBlur"
          @focus="handleSectionTitleFocus"
          maxlength="256"
          show-word-limit
        />

        <!-- 预览模式：显示渲染结果 -->
        <div
          v-else-if="ruleForm.sectionTitle"
          class="preview-container"
          style="width: 100%"
          @click="handleSectionTitlePreviewClick"
        >
          <div
            class="preview"
            v-html="convertMathFormulas(ruleForm.sectionTitle)"
          ></div>
          <div class="preview-hint">点击编辑，支持latex公式</div>
        </div>

        <!-- 空状态：显示输入框 -->
        <el-input
          v-else
          ref="sectionTitleInputRef"
          v-model="ruleForm.sectionTitle"
          placeholder="请输入标题，支持数学公式"
          @blur="handleSectionTitleBlur"
          @focus="handleSectionTitleFocus"
        />
      </el-form-item>
      <el-form-item prop="prText" style="width: 100%">
        <div style="width: 100%">
          <!-- <ClassicEditor
            v-model="Tryimg"
            ref="editorRef"
            style="width: 100%"
          ></ClassicEditor> -->
          <ClassicEditor
            v-model="ruleForm.prText"
            ref="editorRef"
            style="width: 100%"
          ></ClassicEditor>
        </div>
      </el-form-item>
    </el-form>
  </div>
</template>

<style scoped>
.text-content-wrapper {
  margin-bottom: 20px;
  width: 100%;
  :deep(.el-input) {
    --el-input-height: 35px;
  }
}
</style>
